#include "ConsumerDialog.h"
#include "ConsumerPhotoWidget.h"
#include "ConsumerFingerprintWidget.h"
#include "CardLineEdit.h"
#include "../database/dao/DepartmentDao.h"
#include "../database/dao/ConsumerDao.h"
#include "../models/Department.h"
#include "../models/Consumer.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGridLayout>
#include <QTabWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QCheckBox>
#include <QComboBox>
#include <QDateEdit>
#include <QTextEdit>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QHeaderView>
#include <QGroupBox>
#include <QLabel>
#include <QMessageBox>
#include <QCloseEvent>
#include <QDateTime>
#include <QScrollArea>
#include <QDir>
#include <QDebug>
#include <QRegularExpression>
#include <QRegularExpressionValidator>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>

namespace AccessControl {

// ========== AddCardDialog Implementation ==========

AddCardDialog::AddCardDialog(QWidget *parent, bool isFirstCard)
    : QDialog(parent)
    , m_cardNumberEdit(nullptr)
    , m_cardTypeCombo(nullptr)
    , m_isPrimaryCardCheck(nullptr)
    , m_okButton(nullptr)
    , m_cancelButton(nullptr)
    , m_lastCardNumber("")
{
    setWindowTitle(tr("添加卡片"));
    setFixedSize(400, 200);
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);

    initUI();

    // 如果是第一张卡，默认设为主卡且不可取消
    if (isFirstCard) {
        m_isPrimaryCardCheck->setChecked(true);
        m_isPrimaryCardCheck->setEnabled(false);
        m_isPrimaryCardCheck->setToolTip(tr("第一张卡必须设为主卡"));
    }
}

void AddCardDialog::initUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    // 表单布局
    QFormLayout *formLayout = new QFormLayout();

    // 卡号输入
    m_cardNumberEdit = new CardLineEdit(this);
    m_cardNumberEdit->setPlaceholderText(tr("请刷卡或输入卡号"));
    m_cardNumberEdit->setMaxLength(32);

    // 核心逻辑：使用textChanged实时确保只保留最新输入
    connect(m_cardNumberEdit, &QLineEdit::textChanged, this, [this](const QString &text) {
        // 防止递归调用
        static bool isProcessing = false;
        if (isProcessing) return;

        isProcessing = true;

        // 处理多行输入（刷卡器输入可能包含换行符）
        QStringList lines = text.split(QRegularExpression("[\r\n]"), Qt::SkipEmptyParts);
        if (!lines.isEmpty()) {
            QString latestCard = lines.last().trimmed();
            // 只有当显示的文本与最新卡号不一致时才更新
            if (m_cardNumberEdit->text() != latestCard) {
                m_cardNumberEdit->setText(latestCard);
                m_lastCardNumber = latestCard;
            }
        }

        // 调用验证函数以启用/禁用确定按钮
        validateCardNumber();

        isProcessing = false;
    });

    // 回车事件：最终确认
    connect(m_cardNumberEdit, &QLineEdit::returnPressed, this, [this]() {
        QString current = m_cardNumberEdit->text().trimmed();
        if (!current.isEmpty()) {
            m_lastCardNumber = current;
        }
    });

    formLayout->addRow(tr("卡号:"), m_cardNumberEdit);

    // 卡类型
    m_cardTypeCombo = new QComboBox(this);
    m_cardTypeCombo->addItem(tr("IC/ID卡"), 0);
    m_cardTypeCombo->addItem(tr("CPU卡"), 1);
    m_cardTypeCombo->addItem(tr("手机APP"), 2);
    m_cardTypeCombo->addItem(tr("手机NFC"), 3);
    m_cardTypeCombo->addItem(tr("指纹"), 4);
    m_cardTypeCombo->addItem(tr("人脸"), 5);
    m_cardTypeCombo->addItem(tr("手机号"), 6);
    m_cardTypeCombo->addItem(tr("身份证"), 7);
    formLayout->addRow(tr("卡类型:"), m_cardTypeCombo);

    // 是否主卡
    m_isPrimaryCardCheck = new QCheckBox(tr("设为主卡"), this);
    formLayout->addRow(tr(""), m_isPrimaryCardCheck);

    // 按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();

    m_okButton = new QPushButton(tr("确认"), this);
    m_okButton->setEnabled(false);
    connect(m_okButton, &QPushButton::clicked, this, &QDialog::accept);

    m_cancelButton = new QPushButton(tr("取消"), this);
    connect(m_cancelButton, &QPushButton::clicked, this, &QDialog::reject);

    buttonLayout->addWidget(m_okButton);
    buttonLayout->addWidget(m_cancelButton);

    // 添加到主布局
    mainLayout->addLayout(formLayout);
    mainLayout->addLayout(buttonLayout);
}

QString AddCardDialog::getCardNumber() const
{
    return formatCardNumber(m_cardNumberEdit->text());
}

QString AddCardDialog::getCardType() const
{
    return m_cardTypeCombo->currentText();
}

bool AddCardDialog::isPrimaryCard() const
{
    return m_isPrimaryCardCheck->isChecked();
}

void AddCardDialog::setCardNumber(const QString& cardNumber)
{
    if (m_cardNumberEdit) {
        m_cardNumberEdit->setText(cardNumber);
    }
}

void AddCardDialog::setCardType(const QString& cardType)
{
    if (m_cardTypeCombo) {
        int index = m_cardTypeCombo->findText(cardType);
        if (index >= 0) {
            m_cardTypeCombo->setCurrentIndex(index);
        }
    }
}

void AddCardDialog::setPrimaryCard(bool isPrimary)
{
    if (m_isPrimaryCardCheck) {
        m_isPrimaryCardCheck->setChecked(isPrimary);
    }
}

void AddCardDialog::validateCardNumber()
{
    QString text = m_cardNumberEdit->text().trimmed();

    // 验证卡号长度和基本格式
    if (!text.isEmpty()) {
        // 允许字母、数字、连字符和下划线
        QRegularExpression regex("^[A-Za-z0-9_-]+$");
        if (regex.match(text).hasMatch() && text.length() <= 32) {
            m_cardNumberEdit->setStyleSheet("");
            m_okButton->setEnabled(true);
        } else {
            m_cardNumberEdit->setStyleSheet("QLineEdit { border: 2px solid red; }");
            m_okButton->setEnabled(false);
        }
    } else {
        m_cardNumberEdit->setStyleSheet("");
        m_okButton->setEnabled(false);
    }
}

QString AddCardDialog::formatCardNumber(const QString& cardNumber) const
{
    QString trimmed = cardNumber.trimmed();

    // 如果为空，直接返回
    if (trimmed.isEmpty()) {
        return QString();
    }

    // 支持字母数字混合的卡号，不强制要求纯数字
    // 只验证长度，不验证格式
    if (trimmed.length() > 32) {
        return QString();
    }

    return trimmed;
}

// ========== ConsumerDialog Implementation ==========

ConsumerDialog::ConsumerDialog(std::shared_ptr<IDatabaseProvider> dbProvider, QWidget *parent, Mode mode, int consumerId)
    : QDialog(parent)
    , m_mode(mode)
    , m_userId(consumerId)
    , m_dbProvider(dbProvider)
    , m_tabWidget(nullptr)
    // 基本信息控件初始化
    , m_workNumberEdit(nullptr)
    , m_nameEdit(nullptr)
    , m_phoneEdit(nullptr)
    , m_idNumberEdit(nullptr)
    , m_departmentCombo(nullptr)
    , m_attendanceCheck(nullptr)
    , m_accessCheck(nullptr)
    , m_shiftWorkCheck(nullptr)
    , m_validFromEdit(nullptr)
    , m_validUntilEdit(nullptr)
    , m_statusCombo(nullptr)
    // 扩展信息控件初始化
    , m_genderCombo(nullptr)
    , m_workPhoneEdit(nullptr)
    , m_homePhoneEdit(nullptr)
    , m_nationEdit(nullptr)
    , m_religionEdit(nullptr)
    , m_birthplaceEdit(nullptr)
    , m_birthDateEdit(nullptr)
    , m_maritalCombo(nullptr)
    , m_politicalEdit(nullptr)
    , m_educationCombo(nullptr)
    , m_englishNameEdit(nullptr)
    , m_companyEdit(nullptr)
    , m_jobTitleEdit(nullptr)
    , m_technicalLevelEdit(nullptr)
    , m_certificateNameEdit(nullptr)
    , m_certificateNumberEdit(nullptr)
    , m_socialSecurityEdit(nullptr)
    , m_entryDateEdit(nullptr)
    , m_leaveDateEdit(nullptr)
    , m_emailEdit(nullptr)
    , m_addressEdit(nullptr)
    , m_postalCodeEdit(nullptr)
    , m_remarksEdit(nullptr)
    // 卡片管理控件初始化
    , m_cardTable(nullptr)
    , m_addCardButton(nullptr)
    // 功能控件初始化
    , m_photoWidget(nullptr)
    , m_fingerprintWidget(nullptr)
    // 按钮控件初始化
    , m_saveButton(nullptr)
    , m_cancelButton(nullptr)
{
    // 初始化数据库DAO
    if (m_dbProvider) {
        m_departmentDao = std::make_unique<DepartmentDao>(m_dbProvider);
        m_consumerDao = std::make_unique<ConsumerDao>(m_dbProvider);
    }

    // 设置窗口属性
    setWindowTitle(m_mode == Mode::Add ? tr("添加门禁用户") : tr("编辑门禁用户"));
    setMinimumSize(900, 700); // 恢复原来的窗口尺寸
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);

    // 初始化UI
    initUI();

    // 加载部门数据
    loadDepartments();

    // 自动生成工号（仅添加模式）
    if (m_mode == Mode::Add) {
        // 你需要实现getAllWorkNumbers()，返回所有工号字符串列表
        QStringList workNumbers = getAllWorkNumbers();
        autoGenerateWorkNumber(workNumbers);
    }

    // 如果是编辑模式，填充用户数据
    if (m_mode == Mode::Edit && m_userId > 0) {
        fillUserData();
    }
}

ConsumerDialog::~ConsumerDialog()
{
    // 析构函数内容
}

void ConsumerDialog::closeEvent(QCloseEvent *event)
{
    // 关闭前确认
    if (QMessageBox::question(this, tr("确认"), tr("确定要关闭窗口吗？未保存的数据将丢失。"),
                             QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
        event->accept();
    } else {
        event->ignore();
    }
}

void ConsumerDialog::initUI()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    // 创建标签页控件
    m_tabWidget = new QTabWidget(this);

    // 添加各标签页
    m_tabWidget->addTab(createBasicInfoTab(), tr("基本信息"));
    m_tabWidget->addTab(createExtendedInfoTab(), tr("扩展信息"));
    m_tabWidget->addTab(createFingerprintTab(), tr("指纹管理"));

    // 添加标签页控件到主布局
    mainLayout->addWidget(m_tabWidget);

    // 创建按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();

    // 创建保存按钮
    m_saveButton = new QPushButton(tr("保存"), this);
    m_saveButton->setDefault(true);
    connect(m_saveButton, &QPushButton::clicked, this, &ConsumerDialog::saveUser);

    // 创建取消按钮
    m_cancelButton = new QPushButton(tr("取消"), this);
    connect(m_cancelButton, &QPushButton::clicked, this, &ConsumerDialog::cancel);

    // 添加按钮到按钮布局
    buttonLayout->addWidget(m_saveButton);
    buttonLayout->addWidget(m_cancelButton);

    // 添加按钮布局到主布局
    mainLayout->addLayout(buttonLayout);

    // 设置主布局
    setLayout(mainLayout);
}

QWidget* ConsumerDialog::createBasicInfoTab()
{
    QWidget *widget = new QWidget(this);

    // 创建滚动区域包装整个基本信息标签页
    QScrollArea *scrollArea = new QScrollArea(widget);
    scrollArea->setWidgetResizable(true);
    scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 创建滚动内容窗口
    QWidget *scrollContent = new QWidget();
    QHBoxLayout *mainLayout = new QHBoxLayout(scrollContent);

    // 左侧：基本信息表单
    QWidget *formWidget = new QWidget(scrollContent);
    QVBoxLayout *formMainLayout = new QVBoxLayout(formWidget);

    // 创建表单布局
    QFormLayout *formLayout = new QFormLayout();
    formLayout->setFieldGrowthPolicy(QFormLayout::AllNonFixedFieldsGrow);

    // 工号
    m_workNumberEdit = new QLineEdit(formWidget);
    m_workNumberEdit->setPlaceholderText(tr("请输入工号"));
    m_workNumberEdit->setMaxLength(20);
    QRegularExpression regx("^[A-Za-z0-9]{1,20}$");
    QValidator *validator = new QRegularExpressionValidator(regx, m_workNumberEdit);
    m_workNumberEdit->setValidator(validator);
    formLayout->addRow(tr("工号 *:"), m_workNumberEdit);

    // 姓名
    m_nameEdit = new QLineEdit(formWidget);
    m_nameEdit->setPlaceholderText(tr("请输入姓名"));
    m_nameEdit->setMaxLength(50);
    formLayout->addRow(tr("姓名 *:"), m_nameEdit);

    // 部门
    m_departmentCombo = new QComboBox(formWidget);
    m_departmentCombo->addItem(tr("未分配"), -1);
    // 部门数据将在loadDepartments()中加载
    formLayout->addRow(tr("部门:"), m_departmentCombo);

    // 卡片管理（移动到基本信息中）
    QGroupBox *cardGroup = new QGroupBox(tr("卡片管理 (必填)"), formWidget);
    cardGroup->setMinimumHeight(280); // 增加高度以包含按钮
    QVBoxLayout *cardLayout = new QVBoxLayout(cardGroup);

    // 添加标签
    QLabel *cardListLabel = new QLabel(tr("用户卡片列表 (至少添加一张卡片):"), cardGroup);
    cardListLabel->setStyleSheet("font-weight: bold; margin-top: 5px; margin-bottom: 3px; color: #d32f2f;"); // 添加红色表示必填

    // 卡片列表
    m_cardTable = new QTableWidget(0, 4, cardGroup);
    QStringList headers;
    headers << tr("卡号") << tr("卡类型") << tr("是否主卡") << tr("状态");
    m_cardTable->setHorizontalHeaderLabels(headers);
    m_cardTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_cardTable->setSelectionMode(QAbstractItemView::SingleSelection);
    m_cardTable->horizontalHeader()->setStretchLastSection(true);
    m_cardTable->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_cardTable->setMinimumHeight(120); // 恢复较小的高度
    m_cardTable->setMaximumHeight(160); // 恢复较小的高度

    // 设置表格列宽
    m_cardTable->setColumnWidth(0, 120); // 卡号列
    m_cardTable->setColumnWidth(1, 100); // 卡类型列
    m_cardTable->setColumnWidth(2, 80);  // 是否主卡列

    // 设置表格样式
    m_cardTable->setAlternatingRowColors(true);
    m_cardTable->setShowGrid(true);
    m_cardTable->setGridStyle(Qt::SolidLine);

    // 卡片管理按钮（放在卡片组框内）
    QHBoxLayout *cardButtonLayout = new QHBoxLayout();

    // 添加卡片按钮
    m_addCardButton = new QPushButton(tr("添加卡片"), cardGroup);
    m_addCardButton->setFixedHeight(30);
    m_addCardButton->setMinimumWidth(80);
    connect(m_addCardButton, &QPushButton::clicked, this, &ConsumerDialog::addCard);

    // 修改卡片按钮
    QPushButton *editCardButton = new QPushButton(tr("修改卡片"), cardGroup);
    editCardButton->setFixedHeight(30);
    editCardButton->setMinimumWidth(80);
    connect(editCardButton, &QPushButton::clicked, this, &ConsumerDialog::editCard);

    // 删除卡片按钮
    QPushButton *deleteCardButton = new QPushButton(tr("删除卡片"), cardGroup);
    deleteCardButton->setFixedHeight(30);
    deleteCardButton->setMinimumWidth(80);
    connect(deleteCardButton, &QPushButton::clicked, this, &ConsumerDialog::deleteCard);

    cardButtonLayout->addWidget(m_addCardButton);
    cardButtonLayout->addWidget(editCardButton);
    cardButtonLayout->addWidget(deleteCardButton);
    cardButtonLayout->addStretch();

    cardLayout->addWidget(cardListLabel);
    cardLayout->addWidget(m_cardTable, 1); // 给表格分配更多空间
    cardLayout->addLayout(cardButtonLayout); // 按钮在组框内
    cardLayout->setSpacing(8); // 增加组件间距
    cardLayout->setContentsMargins(10, 15, 10, 10); // 设置内边距

    // 创建权限设置和有效期设置的水平布局
    QHBoxLayout *settingsLayout = new QHBoxLayout();

    // 权限设置组框
    QGroupBox *permissionGroup = new QGroupBox(tr("权限设置"), formWidget);
    QVBoxLayout *permissionLayout = new QVBoxLayout(permissionGroup);

    // 考勤启用
    m_attendanceCheck = new QCheckBox(tr("启用考勤"), permissionGroup);
    m_attendanceCheck->setChecked(true);
    permissionLayout->addWidget(m_attendanceCheck);

    // 倒班考勤（放在考勤启用后面，并添加逻辑判断）
    m_shiftWorkCheck = new QCheckBox(tr("启用倒班考勤"), permissionGroup);
    m_shiftWorkCheck->setEnabled(m_attendanceCheck->isChecked()); // 基于考勤启用状态
    permissionLayout->addWidget(m_shiftWorkCheck);

    // 门禁启用
    m_accessCheck = new QCheckBox(tr("启用门禁"), permissionGroup);
    m_accessCheck->setChecked(true);
    permissionLayout->addWidget(m_accessCheck);

    // 连接考勤启用状态变化信号
    connect(m_attendanceCheck, &QCheckBox::toggled, [this](bool checked) {
        m_shiftWorkCheck->setEnabled(checked);
        if (!checked) {
            m_shiftWorkCheck->setChecked(false); // 如果禁用考勤，自动禁用倒班
        }
    });

    // 时间设置组框（移到权限设置右侧）
    QGroupBox *timeGroup = new QGroupBox(tr("有效期设置"), formWidget);
    QFormLayout *timeLayout = new QFormLayout(timeGroup);

    // 起始日期
    m_validFromEdit = new QDateEdit(QDate::currentDate(), timeGroup);
    m_validFromEdit->setCalendarPopup(true);
    timeLayout->addRow(tr("起始日期:"), m_validFromEdit);

    // 截止日期
    m_validUntilEdit = new QDateEdit(QDate(2099, 12, 31), timeGroup);
    m_validUntilEdit->setCalendarPopup(true);
    timeLayout->addRow(tr("截止日期:"), m_validUntilEdit);

    // 将权限设置和有效期设置并排放置
    settingsLayout->addWidget(permissionGroup);
    settingsLayout->addWidget(timeGroup);

    // 用户状态
    m_statusCombo = new QComboBox(formWidget);
    m_statusCombo->addItem(tr("启用"), 0);
    m_statusCombo->addItem(tr("禁用"), 1);
    m_statusCombo->addItem(tr("离职"), 2);

    // 手机号和身份证号（移到底部）
    QGroupBox *contactGroup = new QGroupBox(tr("联系信息"), formWidget);
    QFormLayout *contactLayout = new QFormLayout(contactGroup);

    // 手机号
    m_phoneEdit = new QLineEdit(contactGroup);
    m_phoneEdit->setPlaceholderText(tr("请输入手机号"));
    contactLayout->addRow(tr("手机号:"), m_phoneEdit);

    // 身份证号
    m_idNumberEdit = new QLineEdit(contactGroup);
    m_idNumberEdit->setPlaceholderText(tr("请输入身份证号"));
    contactLayout->addRow(tr("身份证号:"), m_idNumberEdit);

    // 添加到表单布局
    formMainLayout->addLayout(formLayout);
    formMainLayout->addWidget(cardGroup); // 移除stretch factor

    // 卡片管理按钮（放在卡片列表下方）
    // QHBoxLayout *cardButtonLayout = new QHBoxLayout(); // This line is removed as per the edit hint

    // 添加卡片按钮
    // m_addCardButton = new QPushButton(tr("添加卡片"), formWidget); // This line is removed as per the edit hint

    // 修改卡片按钮
    // QPushButton *editCardButton = new QPushButton(tr("修改卡片"), formWidget); // This line is removed as per the edit hint

    // 删除卡片按钮
    // QPushButton *deleteCardButton = new QPushButton(tr("删除卡片"), formWidget); // This line is removed as per the edit hint

    // cardButtonLayout->addWidget(m_addCardButton); // This line is removed as per the edit hint
    // cardButtonLayout->addWidget(editCardButton); // This line is removed as per the edit hint
    // cardButtonLayout->addWidget(deleteCardButton); // This line is removed as per the edit hint
    // cardButtonLayout->addStretch(); // This line is removed as per the edit hint

    // formMainLayout->addLayout(cardButtonLayout); // This line is removed as per the edit hint
    formMainLayout->addLayout(settingsLayout); // 使用水平布局

    QHBoxLayout *statusLayout = new QHBoxLayout();
    statusLayout->addWidget(new QLabel(tr("用户状态:"), formWidget));
    statusLayout->addWidget(m_statusCombo);
    statusLayout->addStretch();
    formMainLayout->addLayout(statusLayout);

    formMainLayout->addWidget(contactGroup); // 移到底部
    formMainLayout->addStretch();

    // 右侧：照片管理
    QWidget *photoWidget = new QWidget(scrollContent);
    QVBoxLayout *photoLayout = new QVBoxLayout(photoWidget);

    // 创建照片管理组件（移除标题文字）
    m_photoWidget = new ConsumerPhotoWidget(photoWidget);

    photoLayout->addWidget(m_photoWidget);
    photoLayout->addStretch();

    // 设置宽度比例：表单占2/3，照片占1/3
    mainLayout->addWidget(formWidget, 2);
    mainLayout->addWidget(photoWidget, 1);

    // 设置滚动内容
    scrollArea->setWidget(scrollContent);

    // 为整个widget设置布局
    QVBoxLayout *widgetLayout = new QVBoxLayout(widget);
    widgetLayout->addWidget(scrollArea);
    widgetLayout->setContentsMargins(0, 0, 0, 0);

    return widget;
}

QWidget* ConsumerDialog::createExtendedInfoTab()
{
    QWidget *widget = new QWidget(this);

    // 创建滚动区域
    QScrollArea *scrollArea = new QScrollArea(widget);
    scrollArea->setWidgetResizable(true);
    scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 创建滚动内容窗口
    QWidget *scrollContent = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(scrollContent);
    layout->setSpacing(20);

    // 个人信息组（改为两列显示）
    QGroupBox *personalGroup = new QGroupBox(tr("个人信息"), scrollContent);
    QGridLayout *personalLayout = new QGridLayout(personalGroup);
    personalLayout->setHorizontalSpacing(20);
    personalLayout->setVerticalSpacing(10);

    int row = 0;

    // 性别（从基本信息移过来）
    personalLayout->addWidget(new QLabel(tr("性别:")), row, 0);
    m_genderCombo = new QComboBox(personalGroup);
    m_genderCombo->addItem(tr("未知"), 0);
    m_genderCombo->addItem(tr("男"), 1);
    m_genderCombo->addItem(tr("女"), 2);
    personalLayout->addWidget(m_genderCombo, row, 1);

    // 民族
    personalLayout->addWidget(new QLabel(tr("民族:")), row, 2);
    m_nationEdit = new QLineEdit(personalGroup);
    m_nationEdit->setPlaceholderText(tr("如：汉族"));
    personalLayout->addWidget(m_nationEdit, row, 3);
    row++;

    // 宗教
    personalLayout->addWidget(new QLabel(tr("宗教:")), row, 0);
    m_religionEdit = new QLineEdit(personalGroup);
    personalLayout->addWidget(m_religionEdit, row, 1);

    // 籍贯
    personalLayout->addWidget(new QLabel(tr("籍贯:")), row, 2);
    m_birthplaceEdit = new QLineEdit(personalGroup);
    personalLayout->addWidget(m_birthplaceEdit, row, 3);
    row++;

    // 出生年月（默认改为1990年1月1日）
    personalLayout->addWidget(new QLabel(tr("出生年月:")), row, 0);
    m_birthDateEdit = new QDateEdit(personalGroup);
    m_birthDateEdit->setCalendarPopup(true);
    m_birthDateEdit->setDate(QDate(1990, 1, 1));
    personalLayout->addWidget(m_birthDateEdit, row, 1);

    // 婚姻状态
    personalLayout->addWidget(new QLabel(tr("婚姻状态:")), row, 2);
    m_maritalCombo = new QComboBox(personalGroup);
    m_maritalCombo->addItem(tr("未知"), 0);
    m_maritalCombo->addItem(tr("未婚"), 1);
    m_maritalCombo->addItem(tr("已婚"), 2);
    m_maritalCombo->addItem(tr("离异"), 3);
    m_maritalCombo->addItem(tr("丧偶"), 4);
    personalLayout->addWidget(m_maritalCombo, row, 3);
    row++;

    // 政治面貌
    personalLayout->addWidget(new QLabel(tr("政治面貌:")), row, 0);
    m_politicalEdit = new QLineEdit(personalGroup);
    m_politicalEdit->setPlaceholderText(tr("如：党员、团员"));
    personalLayout->addWidget(m_politicalEdit, row, 1);

    // 学历
    personalLayout->addWidget(new QLabel(tr("学历:")), row, 2);
    m_educationCombo = new QComboBox(personalGroup);
    m_educationCombo->addItem(tr("未知"), 0);
    m_educationCombo->addItem(tr("小学"), 1);
    m_educationCombo->addItem(tr("初中"), 2);
    m_educationCombo->addItem(tr("高中"), 3);
    m_educationCombo->addItem(tr("中专"), 4);
    m_educationCombo->addItem(tr("大专"), 5);
    m_educationCombo->addItem(tr("本科"), 6);
    m_educationCombo->addItem(tr("硕士"), 7);
    m_educationCombo->addItem(tr("博士"), 8);
    personalLayout->addWidget(m_educationCombo, row, 3);
    row++;

    // 英文名（单独一行，因为可能较长）
    personalLayout->addWidget(new QLabel(tr("英文名:")), row, 0);
    m_englishNameEdit = new QLineEdit(personalGroup);
    personalLayout->addWidget(m_englishNameEdit, row, 1, 1, 3); // 跨3列

    // 联系信息组
    QGroupBox *contactGroup = new QGroupBox(tr("联系信息"), scrollContent);
    QFormLayout *contactLayout = new QFormLayout(contactGroup);
    contactLayout->setHorizontalSpacing(20);
    contactLayout->setVerticalSpacing(10);

    // 工作电话（从基本信息移过来）
    m_workPhoneEdit = new QLineEdit(contactGroup);
    m_workPhoneEdit->setPlaceholderText(tr("请输入工作电话"));
    contactLayout->addRow(tr("工作电话:"), m_workPhoneEdit);

    // 家庭电话（从基本信息移过来）
    m_homePhoneEdit = new QLineEdit(contactGroup);
    m_homePhoneEdit->setPlaceholderText(tr("请输入家庭电话"));
    contactLayout->addRow(tr("家庭电话:"), m_homePhoneEdit);

    // 电子邮箱
    m_emailEdit = new QLineEdit(contactGroup);
    m_emailEdit->setPlaceholderText(tr("<EMAIL>"));
    contactLayout->addRow(tr("电子邮箱:"), m_emailEdit);

    // 通讯地址
    m_addressEdit = new QLineEdit(contactGroup);
    contactLayout->addRow(tr("通讯地址:"), m_addressEdit);

    // 邮编
    m_postalCodeEdit = new QLineEdit(contactGroup);
    m_postalCodeEdit->setPlaceholderText(tr("如：100000"));
    contactLayout->addRow(tr("邮编:"), m_postalCodeEdit);

    // 工作信息组
    QGroupBox *workGroup = new QGroupBox(tr("工作信息"), scrollContent);
    QFormLayout *workLayout = new QFormLayout(workGroup);
    workLayout->setHorizontalSpacing(20);
    workLayout->setVerticalSpacing(10);

    // 单位
    m_companyEdit = new QLineEdit(workGroup);
    workLayout->addRow(tr("单位:"), m_companyEdit);

    // 职称
    m_jobTitleEdit = new QLineEdit(workGroup);
    workLayout->addRow(tr("职称:"), m_jobTitleEdit);

    // 技术等级
    m_technicalLevelEdit = new QLineEdit(workGroup);
    workLayout->addRow(tr("技术等级:"), m_technicalLevelEdit);

    // 入职时间
    m_entryDateEdit = new QDateEdit(workGroup);
    m_entryDateEdit->setCalendarPopup(true);
    m_entryDateEdit->setDate(QDate::currentDate());
    workLayout->addRow(tr("入职时间:"), m_entryDateEdit);

    // 离职时间（默认改为2099年12月31日）
    m_leaveDateEdit = new QDateEdit(workGroup);
    m_leaveDateEdit->setCalendarPopup(true);
    m_leaveDateEdit->setDate(QDate(2099, 12, 31));
    workLayout->addRow(tr("离职时间:"), m_leaveDateEdit);

    // 证件信息组
    QGroupBox *certGroup = new QGroupBox(tr("证件信息"), scrollContent);
    QFormLayout *certLayout = new QFormLayout(certGroup);
    certLayout->setHorizontalSpacing(20);
    certLayout->setVerticalSpacing(10);

    // 证件名称
    m_certificateNameEdit = new QLineEdit(certGroup);
    m_certificateNameEdit->setPlaceholderText(tr("如：驾驶证、护照"));
    certLayout->addRow(tr("证件名称:"), m_certificateNameEdit);

    // 证件号
    m_certificateNumberEdit = new QLineEdit(certGroup);
    certLayout->addRow(tr("证件号:"), m_certificateNumberEdit);

    // 社保号
    m_socialSecurityEdit = new QLineEdit(certGroup);
    certLayout->addRow(tr("社保号:"), m_socialSecurityEdit);

    // 备注
    QGroupBox *remarksGroup = new QGroupBox(tr("备注信息"), scrollContent);
    QVBoxLayout *remarksLayout = new QVBoxLayout(remarksGroup);
    m_remarksEdit = new QTextEdit(remarksGroup);
    m_remarksEdit->setMaximumHeight(100);
    m_remarksEdit->setPlaceholderText(tr("备注信息"));
    remarksLayout->addWidget(m_remarksEdit);

    // 添加到布局
    layout->addWidget(personalGroup);
    layout->addWidget(contactGroup);
    layout->addWidget(workGroup);
    layout->addWidget(certGroup);
    layout->addWidget(remarksGroup);
    layout->addStretch();

    // 设置滚动内容
    scrollArea->setWidget(scrollContent);

    // 主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(widget);
    mainLayout->addWidget(scrollArea);

    return widget;
}

QWidget* ConsumerDialog::createFingerprintTab()
{
    QWidget *widget = new QWidget(this);
    QVBoxLayout *layout = new QVBoxLayout(widget);

    // 创建指纹管理组件
    m_fingerprintWidget = new ConsumerFingerprintWidget(widget);
    layout->addWidget(m_fingerprintWidget);

    return widget;
}

void ConsumerDialog::addCard()
{
    // 检查是否是第一张卡
    bool isFirstCard = (m_cardTable->rowCount() == 0);

    AddCardDialog dialog(this, isFirstCard);
    if (dialog.exec() == QDialog::Accepted) {
        QString cardNumber = dialog.getCardNumber();
        QString cardType = dialog.getCardType();
        bool isPrimary = dialog.isPrimaryCard();

        if (!cardNumber.isEmpty()) {
            // 如果设置为主卡，需要清除其他卡的主卡状态
            if (isPrimary) {
                for (int i = 0; i < m_cardTable->rowCount(); i++) {
                    if (m_cardTable->item(i, 2) && m_cardTable->item(i, 2)->text() == tr("是")) {
                        m_cardTable->setItem(i, 2, new QTableWidgetItem(tr("否")));
                    }
                }
            }

            // 添加到表格
            int row = m_cardTable->rowCount();
            m_cardTable->insertRow(row);

            m_cardTable->setItem(row, 0, new QTableWidgetItem(cardNumber));
            m_cardTable->setItem(row, 1, new QTableWidgetItem(cardType));
            m_cardTable->setItem(row, 2, new QTableWidgetItem(isPrimary ? tr("是") : tr("否")));
            m_cardTable->setItem(row, 3, new QTableWidgetItem(tr("正常")));

            qDebug() << "Added card:" << cardNumber << "Type:" << cardType << "Primary:" << isPrimary;

            // 更新照片文件名
            updatePhotoFileName();
        }
    }
}

void ConsumerDialog::editCard()
{
    int currentRow = m_cardTable->currentRow();
    if (currentRow < 0) {
        QMessageBox::warning(this, tr("提示"), tr("请先选择要修改的卡片！"));
        return;
    }

    // 获取当前卡片信息
    QString currentCardNumber = m_cardTable->item(currentRow, 0)->text();
    QString currentCardType = m_cardTable->item(currentRow, 1)->text();
    bool currentIsPrimary = (m_cardTable->item(currentRow, 2)->text() == tr("是"));

    AddCardDialog dialog(this);

    // 设置对话框的当前值
    dialog.setCardNumber(currentCardNumber);
    dialog.setCardType(currentCardType);
    dialog.setPrimaryCard(currentIsPrimary);

    if (dialog.exec() == QDialog::Accepted) {
        QString cardNumber = dialog.getCardNumber();
        QString cardType = dialog.getCardType();
        bool isPrimary = dialog.isPrimaryCard();

        if (!cardNumber.isEmpty()) {
            // 如果设置为主卡，需要清除其他卡的主卡状态
            if (isPrimary) {
                for (int i = 0; i < m_cardTable->rowCount(); i++) {
                    if (i != currentRow && m_cardTable->item(i, 2) &&
                        m_cardTable->item(i, 2)->text() == tr("是")) {
                        m_cardTable->setItem(i, 2, new QTableWidgetItem(tr("否")));
                    }
                }
            }
            // 如果当前是主卡但修改后不是主卡，需要检查是否还有其他主卡
            else if (currentIsPrimary && !isPrimary) {
                bool hasOtherPrimary = false;
                for (int i = 0; i < m_cardTable->rowCount(); i++) {
                    if (i != currentRow && m_cardTable->item(i, 2) &&
                        m_cardTable->item(i, 2)->text() == tr("是")) {
                        hasOtherPrimary = true;
                        break;
                    }
                }
                if (!hasOtherPrimary) {
                    QMessageBox::warning(this, tr("提示"),
                                       tr("必须要有一张主卡，请先将其他卡片设为主卡！"));
                    return;
                }
            }

            // 更新表格中的数据
            m_cardTable->setItem(currentRow, 0, new QTableWidgetItem(cardNumber));
            m_cardTable->setItem(currentRow, 1, new QTableWidgetItem(cardType));
            m_cardTable->setItem(currentRow, 2, new QTableWidgetItem(isPrimary ? tr("是") : tr("否")));

            qDebug() << "Updated card:" << cardNumber << "Type:" << cardType << "Primary:" << isPrimary;

            // 更新照片文件名
            updatePhotoFileName();
        }
    }
}

void ConsumerDialog::deleteCard()
{
    int currentRow = m_cardTable->currentRow();
    if (currentRow < 0) {
        QMessageBox::warning(this, tr("提示"), tr("请先选择要删除的卡片！"));
        return;
    }

    QString cardNumber = m_cardTable->item(currentRow, 0)->text();
    bool isPrimary = (m_cardTable->item(currentRow, 2)->text() == tr("是"));

    // 检查是否是主卡且还有其他卡片
    if (isPrimary && m_cardTable->rowCount() > 1) {
        QMessageBox::warning(this, tr("无法删除"),
                           tr("不能删除主卡，请先将其他卡片设为主卡后再删除此卡！"));
        return;
    }

    // 如果只有一张卡，不允许删除
    if (m_cardTable->rowCount() == 1) {
        QMessageBox::warning(this, tr("无法删除"),
                           tr("至少需要保留一张卡片！"));
        return;
    }

    int ret = QMessageBox::question(this, tr("确认删除"),
                                   tr("确定要删除卡号为 %1 的卡片吗？").arg(cardNumber),
                                   QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        m_cardTable->removeRow(currentRow);
        qDebug() << "Deleted card:" << cardNumber;

        // 更新照片文件名
        updatePhotoFileName();
    }
}

void ConsumerDialog::fillUserData()
{
    if (!m_consumerDao || m_userId <= 0) {
        qDebug() << "fillUserData: ConsumerDao not initialized or invalid user ID:" << m_userId;
        return;
    }

    try {
        // 从数据库加载用户数据
        auto consumerOpt = m_consumerDao->getById(m_userId);
        if (!consumerOpt.has_value()) {
            qDebug() << "fillUserData: Consumer not found with ID:" << m_userId;
            QMessageBox::warning(this, tr("错误"), tr("无法找到指定的用户数据！"));
            return;
        }

        const Consumer& consumer = consumerOpt.value();

        // 填充基本信息
        m_workNumberEdit->setText(consumer.workNumber());
        m_nameEdit->setText(consumer.realName());
        m_phoneEdit->setText(consumer.phoneNumber());
        m_idNumberEdit->setText(consumer.idNumber());

        // 设置权限和状态
        m_accessCheck->setChecked(consumer.accessEnabled());
        m_attendanceCheck->setChecked(consumer.attendanceEnabled());
        m_shiftWorkCheck->setChecked(consumer.shiftWork());

        // 设置有效期
        m_validFromEdit->setDate(consumer.validFrom());
        m_validUntilEdit->setDate(consumer.validUntil());

        // 设置状态
        int statusIndex = static_cast<int>(consumer.status());
        for (int i = 0; i < m_statusCombo->count(); i++) {
            if (m_statusCombo->itemData(i).toInt() == statusIndex) {
                m_statusCombo->setCurrentIndex(i);
                break;
            }
        }

        // 设置部门
        int departmentId = consumer.departmentId();
        for (int i = 0; i < m_departmentCombo->count(); i++) {
            if (m_departmentCombo->itemData(i).toInt() == departmentId) {
                m_departmentCombo->setCurrentIndex(i);
                break;
            }
        }

        // 加载卡片信息
        loadUserCards(m_userId);

        // 编辑模式下禁用工号编辑
        if (m_mode == Mode::Edit) {
            m_workNumberEdit->setReadOnly(true);
        }

        qDebug() << "fillUserData: Successfully loaded data for user:" << consumer.workNumber();

    } catch (const std::exception& e) {
        qDebug() << "fillUserData: Exception:" << e.what();
        QMessageBox::critical(this, tr("错误"), tr("加载用户数据时发生错误：") + QString(e.what()));
    }
}

// 自动生成工号逻辑（在添加用户对话框弹出时调用）
void ConsumerDialog::autoGenerateWorkNumber(const QStringList &workNumbers)
{
    int maxNum = 0;
    for (const QString &num : workNumbers) {
        bool ok = false;
        int n = num.toInt(&ok);
        if (ok && n > maxNum) maxNum = n;
    }
    if (maxNum == 0) {
        m_workNumberEdit->setText("1");
    } else {
        m_workNumberEdit->setText(QString::number(maxNum + 1));
    }
}

// 获取所有工号
QStringList ConsumerDialog::getAllWorkNumbers()
{
    QStringList workNumbers;

    if (!m_consumerDao) {
        qDebug() << "getAllWorkNumbers: ConsumerDao not initialized";
        return workNumbers;
    }

    try {
        // 通过DAO获取所有用户
        auto consumers = m_consumerDao->getAllConsumers();

        // 提取工号
        for (const auto& consumer : consumers) {
            workNumbers.append(consumer.workNumber());
        }

        qDebug() << "getAllWorkNumbers: Retrieved" << workNumbers.size() << "work numbers";

    } catch (const std::exception& e) {
        qDebug() << "getAllWorkNumbers: Exception:" << e.what();
    }

    return workNumbers;
}

void ConsumerDialog::saveUser()
{
    // 验证表单
    if (!validateForm()) {
        return;
    }

    // 保存用户数据
    if (saveUserToDatabase()) {
        QMessageBox::information(this, tr("成功"), tr("门禁用户信息保存成功！"));
        accept();
    } else {
        QMessageBox::critical(this, tr("错误"), tr("保存门禁用户信息失败，请重试！"));
    }
}

void ConsumerDialog::cancel()
{
    reject();
}

// 表单校验完善
bool ConsumerDialog::validateForm()
{
    // 工号校验
    QString workNum = m_workNumberEdit->text().trimmed();
    if (workNum.isEmpty() || workNum.length() > 20) {
        QMessageBox::warning(this, tr("验证失败"), tr("工号不能为空且不能超过20个字符！"));
        m_workNumberEdit->setFocus();
        return false;
    }
    if (!QRegularExpression("^[A-Za-z0-9]+$").match(workNum).hasMatch()) {
        QMessageBox::warning(this, tr("验证失败"), tr("工号只能包含字母和数字！"));
        m_workNumberEdit->setFocus();
        return false;
    }
    // 姓名校验
    QString name = m_nameEdit->text().trimmed();
    if (name.isEmpty() || name.length() > 50) {
        QMessageBox::warning(this, tr("验证失败"), tr("姓名不能为空且不能超过50个字符！"));
        m_nameEdit->setFocus();
        return false;
    }

    // 卡片必填校验
    if (m_cardTable->rowCount() == 0) {
        QMessageBox::warning(this, tr("验证失败"), tr("至少需要添加一张卡片！"));
        // 切换到基本信息标签页，聚焦到添加卡片按钮
        if (m_tabWidget) {
            m_tabWidget->setCurrentIndex(0); // 切换到基本信息标签页
        }
        if (m_addCardButton) {
            m_addCardButton->setFocus();
        }
        return false;
    }

    qDebug() << "Form validation passed";
    return true;
}

bool ConsumerDialog::saveUserToDatabase()
{
    if (!m_consumerDao) {
        qDebug() << "ConsumerDao is not initialized";
        return false;
    }

    try {
        // 开始数据库事务
        QSqlDatabase db = m_dbProvider->getDatabase();
        if (!db.transaction()) {
            qDebug() << "Failed to start database transaction";
            return false;
        }

        // 创建Consumer对象
        Consumer consumer;

        // 填充基本信息
        consumer.setWorkNumber(m_workNumberEdit->text().trimmed());
        consumer.setRealName(m_nameEdit->text().trimmed());
        consumer.setPhoneNumber(m_phoneEdit->text().trimmed());
        consumer.setIdNumber(m_idNumberEdit->text().trimmed());

        // 获取部门ID
        int departmentId = m_departmentCombo->currentData().toInt();
        consumer.setDepartmentId(departmentId);

        // 设置权限和状态
        consumer.setAccessEnabled(m_accessCheck->isChecked());
        consumer.setAttendanceEnabled(m_attendanceCheck->isChecked());
        consumer.setShiftWork(m_shiftWorkCheck->isChecked());

        // 设置有效期
        consumer.setValidFrom(m_validFromEdit->date());
        consumer.setValidUntil(m_validUntilEdit->date());

        // 设置状态
        int statusValue = m_statusCombo->currentData().toInt();
        consumer.setStatus(static_cast<Consumer::Status>(statusValue));

        // 保存Consumer到数据库
        bool success = false;
        int consumerId = 0;

        qDebug() << "=== Saving Consumer ===";
        qDebug() << "Work Number:" << consumer.workNumber();
        qDebug() << "Real Name:" << consumer.realName();
        qDebug() << "Department ID:" << consumer.departmentId();
        qDebug() << "Phone Number:" << consumer.phoneNumber();
        qDebug() << "ID Number:" << consumer.idNumber();

        if (m_mode == Mode::Add) {
            // 添加模式：创建新用户
            success = m_consumerDao->create(consumer);
            if (success) {
                consumerId = consumer.id();
                qDebug() << "Consumer created successfully with ID:" << consumerId;
            } else {
                qDebug() << "Failed to create consumer in database";
                db.rollback();
                return false;
            }
        } else {
            // 编辑模式：更新现有用户
            consumer.setId(m_userId);
            success = m_consumerDao->update(consumer);
            if (success) {
                consumerId = m_userId;
                qDebug() << "Consumer updated successfully";
            } else {
                qDebug() << "Failed to update consumer in database";
                db.rollback();
                return false;
            }
        }

        // 保存卡片数据
        qDebug() << "=== Saving Cards ===";
        qDebug() << "Card count:" << m_cardTable->rowCount();
        if (m_cardTable->rowCount() > 0) {
            if (!saveCardsToDatabase(consumerId)) {
                qDebug() << "Failed to save cards to database";
                db.rollback();
                return false;
            }
        } else {
            qDebug() << "No cards to save (0 cards in table)";
        }

        // 提交事务
        if (!db.commit()) {
            qDebug() << "Failed to commit database transaction";
            return false;
        }

        // 保存照片文件（如果有）
        if (m_photoWidget && m_photoWidget->hasPhoto()) {
            QString photoFileName = getPhotoFileName();
            if (!photoFileName.isEmpty()) {
                QString photoDirectory = "photos"; // 可以配置照片保存目录

                // 创建照片目录
                QDir dir;
                if (!dir.exists(photoDirectory)) {
                    dir.mkpath(photoDirectory);
                }

                if (m_photoWidget->savePhotoToFile(photoFileName, photoDirectory)) {
                    qDebug() << "照片已保存为:" << photoFileName << ".jpg";
                } else {
                    qDebug() << "照片保存失败";
                }
            } else {
                qDebug() << "无法确定照片文件名，照片保存失败";
            }
        }

        qDebug() << "saveUserToDatabase completed successfully";
        return true;

    } catch (const std::exception& e) {
        qDebug() << "Exception in saveUserToDatabase:" << e.what();
        QSqlDatabase db = m_dbProvider->getDatabase();
        db.rollback();
        return false;
    }
}

QString ConsumerDialog::getCurrentDateTime()
{
    return QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
}

QString ConsumerDialog::getPhotoFileName() const
{
    // 优先使用人脸类型的卡号
    QString faceCardNumber = findCardNumberByType(tr("人脸"));
    if (!faceCardNumber.isEmpty()) {
        return faceCardNumber;
    }

    // 如果没有人脸类型，使用主卡
    QString primaryCardNumber = getPrimaryCardNumber();
    if (!primaryCardNumber.isEmpty()) {
        return primaryCardNumber;
    }

    // 如果没有卡片，返回空字符串（不应该发生，因为卡片是必填的）
    qWarning() << "No card found for photo naming - this should not happen as cards are required";
    return QString();
}

void ConsumerDialog::updatePhotoFileName()
{
    // 当卡片信息变更时，通知照片控件更新文件名
    // 这里可以发送信号或者直接调用照片控件的更新方法
    QString newFileName = getPhotoFileName();
    qDebug() << "Photo filename updated to:" << newFileName;

    // 触发照片变更信号，让外部知道文件名可能已变更
    // 实际保存时会使用新的文件名
}

QString ConsumerDialog::findCardNumberByType(const QString& cardType) const
{
    for (int i = 0; i < m_cardTable->rowCount(); i++) {
        QTableWidgetItem* typeItem = m_cardTable->item(i, 1);
        QTableWidgetItem* numberItem = m_cardTable->item(i, 0);

        if (typeItem && numberItem && typeItem->text() == cardType) {
            return numberItem->text();
        }
    }
    return QString();
}

QString ConsumerDialog::getPrimaryCardNumber() const
{
    for (int i = 0; i < m_cardTable->rowCount(); i++) {
        QTableWidgetItem* primaryItem = m_cardTable->item(i, 2);
        QTableWidgetItem* numberItem = m_cardTable->item(i, 0);

        if (primaryItem && numberItem && primaryItem->text() == tr("是")) {
            return numberItem->text();
        }
    }
    return QString();
}

void ConsumerDialog::loadDepartments()
{
    if (!m_departmentDao || !m_departmentCombo) {
        return;
    }

    try {
        // 清除现有项目，保留"未分配"选项
        while (m_departmentCombo->count() > 1) {
            m_departmentCombo->removeItem(1);
        }

        // 先更新所有部门的路径（确保多级路径正确）
        m_departmentDao->updateDepartmentPaths(0); // 0表示更新所有部门

        // 从数据库加载所有启用的部门
        QList<Department> departments = m_departmentDao->findActive();

        qDebug() << "Loading" << departments.size() << "departments";

        for (const Department& dept : departments) {
            // 使用fullPath()来显示多级部门结构，如果没有则构建层级显示
            QString displayText = dept.fullPath();

            if (displayText.isEmpty()) {
                // 如果fullPath为空，手动构建路径
                displayText = buildDepartmentPath(dept, departments);
            }

            // 确保显示文本使用"\"分隔符
            displayText = displayText.replace('/', '\\');

            qDebug() << "Department:" << dept.name() << "Full path:" << displayText << "Level:" << dept.level();

            m_departmentCombo->addItem(displayText, dept.id());
        }

        qDebug() << "Loaded" << departments.size() << "departments into combo box";

    } catch (const std::exception& e) {
        qWarning() << "Failed to load departments:" << e.what();
        // 添加一个错误提示项
        m_departmentCombo->addItem(tr("加载部门失败"), -2);
    }
}

QString ConsumerDialog::buildDepartmentPath(const Department& dept, const QList<Department>& allDepartments) const
{
    if (dept.parentId() <= 0) {
        // 根部门
        return dept.name();
    }

    // 查找父部门
    for (const Department& parent : allDepartments) {
        if (parent.id() == dept.parentId()) {
            // 递归构建父路径
            QString parentPath = buildDepartmentPath(parent, allDepartments);
            return parentPath + "\\" + dept.name();
        }
    }

    // 找不到父部门，返回部门名称
    return dept.name();
}

void ConsumerDialog::loadUserCards(int consumerId)
{
    if (!m_dbProvider || consumerId <= 0) {
        qDebug() << "loadUserCards: Invalid parameters";
        return;
    }

    try {
        // 清空现有卡片表格
        m_cardTable->setRowCount(0);

        // 直接查询consumer_cards表
        QSqlDatabase db = m_dbProvider->getDatabase();
        QSqlQuery query(db);
        query.prepare(
            "SELECT card_number, card_type, is_primary, status "
            "FROM consumer_cards WHERE consumer_id = ? ORDER BY is_primary DESC, id"
        );
        query.addBindValue(consumerId);

        if (!query.exec()) {
            qDebug() << "loadUserCards: Failed to query cards:" << query.lastError().text();
            return;
        }

        // 填充卡片表格
        int row = 0;
        while (query.next()) {
            m_cardTable->insertRow(row);

            // 卡号
            QString cardNumber = query.value("card_number").toString();
            m_cardTable->setItem(row, 0, new QTableWidgetItem(cardNumber));

            // 卡类型
            int cardType = query.value("card_type").toInt();
            QString cardTypeName = getCardTypeName(cardType);
            m_cardTable->setItem(row, 1, new QTableWidgetItem(cardTypeName));

            // 是否主卡
            bool isPrimary = query.value("is_primary").toBool();
            m_cardTable->setItem(row, 2, new QTableWidgetItem(isPrimary ? tr("是") : tr("否")));

            // 状态
            int status = query.value("status").toInt();
            QString statusName = getCardStatusName(status);
            m_cardTable->setItem(row, 3, new QTableWidgetItem(statusName));

            row++;
        }

        qDebug() << "loadUserCards: Loaded" << row << "cards for user" << consumerId;

    } catch (const std::exception& e) {
        qDebug() << "loadUserCards: Exception:" << e.what();
    }
}

QString ConsumerDialog::getCardTypeName(int cardType)
{
    switch (cardType) {
        case 0: return tr("IC/ID卡");
        case 1: return tr("CPU卡");
        case 2: return tr("手机APP");
        case 3: return tr("手机NFC");
        case 4: return tr("指纹");
        case 5: return tr("人脸");
        case 6: return tr("手机号");
        case 7: return tr("身份证");
        default: return tr("未知");
    }
}

QString ConsumerDialog::getCardStatusName(int status)
{
    switch (status) {
        case 0: return tr("正常");
        case 1: return tr("挂失");
        case 2: return tr("注销");
        default: return tr("未知");
    }
}

bool ConsumerDialog::saveCardsToDatabase(int consumerId)
{
    if (!m_dbProvider || consumerId <= 0) {
        qDebug() << "saveCardsToDatabase: Invalid parameters";
        return false;
    }

    try {
        QSqlDatabase db = m_dbProvider->getDatabase();

        // 在编辑模式下，先删除所有现有卡片，然后重新插入
        if (m_mode == Mode::Edit) {
            QSqlQuery deleteQuery(db);
            deleteQuery.prepare("DELETE FROM consumer_cards WHERE consumer_id = ?");
            deleteQuery.addBindValue(consumerId);
            if (!deleteQuery.exec()) {
                qDebug() << "Failed to delete existing cards:" << deleteQuery.lastError().text();
                return false;
            }
        }

        // 插入所有卡片
        for (int i = 0; i < m_cardTable->rowCount(); ++i) {
            QString cardNumber = m_cardTable->item(i, 0)->text().trimmed();
            QString cardType = m_cardTable->item(i, 1)->text().trimmed();
            bool isPrimary = (m_cardTable->item(i, 2)->text() == tr("是"));

            if (cardNumber.isEmpty()) {
                qDebug() << "Empty card number at row" << i;
                continue;
            }

            // 插入卡片数据 - 注意：card_number字段应该是字符串类型
            QSqlQuery insertQuery(db);
            insertQuery.prepare(
                "INSERT INTO consumer_cards (consumer_id, card_number, card_type, is_primary, status, "
                "valid_from, valid_until, created_at, updated_at) "
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"
            );
            insertQuery.addBindValue(consumerId);
            insertQuery.addBindValue(cardNumber); // 确保作为字符串保存
            insertQuery.addBindValue(getCardTypeIndex(cardType));
            insertQuery.addBindValue(isPrimary ? 1 : 0);
            insertQuery.addBindValue(0); // status: 0=正常
            insertQuery.addBindValue(QDate::currentDate());
            insertQuery.addBindValue(QDate(2099, 12, 31));
            insertQuery.addBindValue(QDateTime::currentDateTime());
            insertQuery.addBindValue(QDateTime::currentDateTime());

            if (!insertQuery.exec()) {
                qDebug() << "Failed to insert card:" << cardNumber << "Error:" << insertQuery.lastError().text();
                qDebug() << "Query:" << insertQuery.executedQuery();
                qDebug() << "Values:" << consumerId << cardNumber << getCardTypeIndex(cardType) << isPrimary;
                return false;
            }

            qDebug() << "Saved card successfully:" << cardNumber << "Type:" << cardType << "Primary:" << isPrimary;
        }

        qDebug() << "saveCardsToDatabase: Successfully saved" << m_cardTable->rowCount() << "cards for consumer" << consumerId;
        return true;

    } catch (const std::exception& e) {
        qDebug() << "Exception in saveCardsToDatabase:" << e.what();
        return false;
    }
}

int ConsumerDialog::getCardTypeIndex(const QString& cardType)
{
    if (cardType == tr("IC/ID卡")) return 0;
    if (cardType == tr("CPU卡")) return 1;
    if (cardType == tr("手机APP")) return 2;
    if (cardType == tr("手机NFC")) return 3;
    if (cardType == tr("指纹")) return 4;
    if (cardType == tr("人脸")) return 5;
    if (cardType == tr("手机号")) return 6;
    if (cardType == tr("身份证")) return 7;
    return 0; // 默认IC/ID卡
}

} // namespace AccessControl
