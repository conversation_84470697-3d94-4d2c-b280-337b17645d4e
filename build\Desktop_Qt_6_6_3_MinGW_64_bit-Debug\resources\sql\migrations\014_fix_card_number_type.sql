-- 迁移文件：014_fix_card_number_type.sql
-- 描述：修复consumer_cards表中card_number字段类型，从BIGINT改为VARCHAR以支持字母数字混合卡号
-- 创建时间：2025-01-25
-- 作者：AI Assistant

-- ========== 修复consumer_cards表结构 ==========

-- SQLite不支持直接修改列类型，需要重建表

-- 1. 创建临时表
CREATE TABLE IF NOT EXISTS consumer_cards_temp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER NOT NULL,            -- 消费者ID
    card_number VARCHAR(32) NOT NULL,        -- 卡号（支持字母数字混合，最长32字符）
    card_type INTEGER NOT NULL DEFAULT 0,    -- 卡类型：0-IC/ID卡, 1-CPU卡, 2-手机APP, 3-手机NFC, 4-指纹, 5-人脸, 6-手机号, 7-身份证
    is_primary BOOLEAN DEFAULT 0,            -- 是否主卡
    is_biometric BOOLEAN DEFAULT 0,          -- 是否生物识别卡（指纹、人脸）
    status INTEGER NOT NULL DEFAULT 0,       -- 状态：0-正常, 1-挂失, 2-注销
    valid_from DATE DEFAULT (date('now')),   -- 有效期开始
    valid_until DATE DEFAULT '2099-12-31',   -- 有效期结束
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE
);

-- 2. 复制现有数据（如果表存在）
INSERT INTO consumer_cards_temp (consumer_id, card_number, card_type, is_primary, is_biometric, status, valid_from, valid_until, created_at, updated_at)
SELECT consumer_id, CAST(card_number AS TEXT), card_type, is_primary, is_biometric, status, valid_from, valid_until, created_at, updated_at 
FROM consumer_cards 
WHERE EXISTS (SELECT name FROM sqlite_master WHERE type='table' AND name='consumer_cards');

-- 3. 删除旧表
DROP TABLE IF EXISTS consumer_cards;

-- 4. 重命名临时表
ALTER TABLE consumer_cards_temp RENAME TO consumer_cards;

-- 5. 重新创建索引
CREATE INDEX IF NOT EXISTS idx_consumer_cards_consumer_id ON consumer_cards(consumer_id);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_card_number ON consumer_cards(card_number);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_card_type ON consumer_cards(card_type);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_status ON consumer_cards(status);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_is_primary ON consumer_cards(is_primary);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_is_biometric ON consumer_cards(is_biometric);

-- 6. 重新创建触发器
CREATE TRIGGER IF NOT EXISTS trigger_consumer_cards_update 
AFTER UPDATE ON consumer_cards
FOR EACH ROW
BEGIN
    UPDATE consumer_cards SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- 7. 创建唯一约束
CREATE UNIQUE INDEX IF NOT EXISTS idx_consumer_cards_unique_card_number ON consumer_cards(card_number); 