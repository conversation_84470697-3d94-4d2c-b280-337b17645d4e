#include "CardLineEdit.h"
#include <QKeyEvent>
#include <QRegularExpression>

CardLineEdit::CardLineEdit(QWidget *parent)
    : QLineEdit(parent)
{
}

void CardLineEdit::keyPressEvent(QKeyEvent *event)
{
    QLineEdit::keyPressEvent(event);
    if (event->key() == Qt::Key_Return || event->key() == Qt::Key_Enter) {
        QString text = this->text();
        QStringList lines = text.split(QRegularExpression("[\r\n]"), Qt::SkipEmptyParts);
        if (!lines.isEmpty()) {
            QString latest = lines.last().trimmed();
            if (this->text() != latest) {
                this->setText(latest);
            }
        }
    }
} 