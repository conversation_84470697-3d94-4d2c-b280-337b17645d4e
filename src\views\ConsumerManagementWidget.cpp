#include "ConsumerManagementWidget.h"
#include "ui_ConsumerManagementWidget.h"
#include "ConsumerDialog.h"
#include "../database/dao/DepartmentDao.h"
#include <QTableWidget>
#include <QHeaderView>
#include <QMessageBox>
#include <QDateTime>
#include <QDebug>

namespace AccessControl {

ConsumerManagementWidget::ConsumerManagementWidget(std::shared_ptr<IDatabaseProvider> dbProvider, QWidget *parent)
    : QWidget(parent),
    ui(new Ui::ConsumerManagementWidget),
    m_databaseProvider(dbProvider)
{
    ui->setupUi(this);
    
    // 初始化DAO
    m_consumerDao = std::make_unique<ConsumerDao>(m_databaseProvider);
    m_departmentDao = std::make_unique<DepartmentDao>(m_databaseProvider);
    
    // 获取UI元素引用
    m_consumerTable = ui->consumerTableWidget;
    m_nameSearchEdit = ui->nameSearchEdit;
    m_cardSearchEdit = ui->cardSearchEdit;
    m_deptSearchCombo = ui->deptSearchCombo;
    m_totalLabel = ui->totalLabel;
    m_statusLabel = ui->statusLabel;
    
    // 获取按钮引用
    m_batchAddButton = ui->batchAddButton;
    m_addButton = ui->addButton;
    m_editButton = ui->editButton;
    m_deleteButton = ui->deleteButton;
    m_printButton = ui->printButton;
    m_exportButton = ui->exportButton;
    m_importButton = ui->importButton;
    m_lostButton = ui->lostButton;
    m_searchButton = ui->searchButton;
    m_filterButton = ui->filterButton;
    m_clearFilterButton = ui->clearFilterButton;

    
    // 设置界面
    setupUi();
    setupConnections();
    
    // 加载数据
    loadDepartments();
    loadConsumers();
}

ConsumerManagementWidget::~ConsumerManagementWidget()
{
    delete ui;
}

void ConsumerManagementWidget::setupUi()
{
    setupTableHeaders();
    
    // 设置表格属性
    m_consumerTable->setAlternatingRowColors(true);
    m_consumerTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_consumerTable->setSelectionMode(QAbstractItemView::ExtendedSelection);
    m_consumerTable->setSortingEnabled(true);
    m_consumerTable->horizontalHeader()->setStretchLastSection(true);
    
    // 设置表格列调整模式
    m_consumerTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    
    // 更均匀地设置列宽
    m_consumerTable->setColumnWidth(0, 90);   // 工号
    m_consumerTable->setColumnWidth(1, 90);   // 姓名
    m_consumerTable->setColumnWidth(2, 100);  // 卡号
    m_consumerTable->setColumnWidth(3, 70);   // 考勤
    m_consumerTable->setColumnWidth(4, 70);   // 倒班
    m_consumerTable->setColumnWidth(5, 70);   // 门禁
    m_consumerTable->setColumnWidth(6, 90);   // 起始日期
    m_consumerTable->setColumnWidth(7, 90);   // 截止日期
    m_consumerTable->setColumnWidth(8, 100);  // 部门
    m_consumerTable->setColumnWidth(9, 100);  // 手机号
    m_consumerTable->setColumnWidth(10, 120); // 身份证
    
    updateButtonStates();
}

void ConsumerManagementWidget::setupConnections()
{
    // 功能按钮连接
    connect(m_batchAddButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onBatchAdd);
    connect(m_addButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onAdd);
    connect(m_editButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onEdit);
    connect(m_deleteButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onDelete);
    connect(m_printButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onPrint);
    connect(m_exportButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onExport);
    connect(m_importButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onImport);
    connect(m_lostButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onLost);
    connect(m_searchButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onSearch);
    
    // 搜索过滤连接
    connect(m_filterButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onFilter);
    connect(m_clearFilterButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onClearFilter);
    connect(m_nameSearchEdit, &QLineEdit::textChanged, this, &ConsumerManagementWidget::onNameSearchChanged);
    connect(m_cardSearchEdit, &QLineEdit::textChanged, this, &ConsumerManagementWidget::onCardSearchChanged);
    connect(m_deptSearchCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), 
            this, &ConsumerManagementWidget::onDeptSearchChanged);
    
    // 表格连接
    connect(m_consumerTable, &QTableWidget::cellDoubleClicked, this, &ConsumerManagementWidget::onTableDoubleClicked);
    connect(m_consumerTable, &QTableWidget::itemSelectionChanged, this, &ConsumerManagementWidget::onTableSelectionChanged);
    
    // 搜索框回车键
    connect(m_nameSearchEdit, &QLineEdit::returnPressed, this, &ConsumerManagementWidget::onFilter);
    connect(m_cardSearchEdit, &QLineEdit::returnPressed, this, &ConsumerManagementWidget::onFilter);
}

void ConsumerManagementWidget::setupTableHeaders()
{
    QStringList headers;
    headers << "工号" << "姓名" << "卡号" << "考勤" << "倒班" << "门禁" 
            << "起始日期" << "截止日期" << "部门" << "手机号" << "身份证";
    
    m_consumerTable->setColumnCount(headers.size());
    m_consumerTable->setHorizontalHeaderLabels(headers);
}

void ConsumerManagementWidget::loadDepartments()
{
    m_deptSearchCombo->clear();
    m_deptSearchCombo->addItem("全部部门", -1);
    
    try {
        auto departments = m_departmentDao->findAll();
        for (const auto& dept : departments) {
            m_deptSearchCombo->addItem(dept.name(), dept.id());
        }
    } catch (const std::exception& e) {
        qDebug() << "Load departments failed:" << e.what();
        m_statusLabel->setText("加载部门信息失败");
    }
}

void ConsumerManagementWidget::loadConsumers()
{
    qDebug() << "ConsumerManagementWidget::loadConsumers: Starting to load consumers...";
    try {
        m_allConsumers = m_consumerDao->getAllConsumers();
        qDebug() << "ConsumerManagementWidget::loadConsumers: Retrieved" << m_allConsumers.size() << "consumers from database";
        m_filteredConsumers = m_allConsumers;
        populateConsumerTable(m_filteredConsumers);
        updateStatusBar();
        m_statusLabel->setText("门禁卡持有者数据加载完成");
        qDebug() << "ConsumerManagementWidget::loadConsumers: Consumer loading completed successfully";
    } catch (const std::exception& e) {
        qDebug() << "Load consumers failed:" << e.what();
        m_statusLabel->setText("加载门禁卡持有者数据失败");
        QMessageBox::critical(this, "错误", "加载门禁卡持有者数据失败：" + QString(e.what()));
    }
}

void ConsumerManagementWidget::refreshConsumerTable()
{
    loadConsumers();
}

void ConsumerManagementWidget::populateConsumerTable(const std::vector<Consumer>& consumers)
{
    m_consumerTable->setRowCount(0);
    m_consumerTable->setRowCount(consumers.size());
    
    for (size_t i = 0; i < consumers.size(); ++i) {
        fillConsumerRow(i, consumers[i]);
    }
    
    updateStatusBar();
}

void ConsumerManagementWidget::fillConsumerRow(int row, const Consumer& consumer)
{
    // 工号
    m_consumerTable->setItem(row, 0, new QTableWidgetItem(consumer.workNumber()));
    
    // 姓名
    m_consumerTable->setItem(row, 1, new QTableWidgetItem(consumer.getDisplayName()));
    
    // 卡号 (这里暂时显示工号，后续可以从operator_cards表获取)
    m_consumerTable->setItem(row, 2, new QTableWidgetItem(consumer.workNumber()));
    
    // 考勤
    m_consumerTable->setItem(row, 3, new QTableWidgetItem(formatBoolValue(consumer.attendanceEnabled())));
    
    // 倒班
    m_consumerTable->setItem(row, 4, new QTableWidgetItem(formatBoolValue(consumer.shiftWork())));
    
    // 门禁
    m_consumerTable->setItem(row, 5, new QTableWidgetItem(formatBoolValue(consumer.accessEnabled())));
    
    // 起始日期
    m_consumerTable->setItem(row, 6, new QTableWidgetItem(consumer.validFrom().toString("yyyy-MM-dd")));
    
    // 截止日期
    m_consumerTable->setItem(row, 7, new QTableWidgetItem(consumer.validUntil().toString("yyyy-MM-dd")));
    
    // 部门
    m_consumerTable->setItem(row, 8, new QTableWidgetItem(getDepartmentName(consumer.departmentId())));
    
    // 手机号
    m_consumerTable->setItem(row, 9, new QTableWidgetItem(consumer.phoneNumber()));
    
    // 身份证
    m_consumerTable->setItem(row, 10, new QTableWidgetItem(consumer.idNumber()));
    
    // 存储用户ID到行数据中
    m_consumerTable->item(row, 0)->setData(Qt::UserRole, consumer.id());
}

QString ConsumerManagementWidget::getDepartmentName(int departmentId)
{
    if (departmentId <= 0) return "未分配";
    
    try {
        // 获取部门的完整路径
        auto departmentPath = m_departmentDao->getDepartmentPath(departmentId);
        
        if (departmentPath.isEmpty()) {
            return "未知部门";
        }
        
        // 构建完整路径字符串，用"\"分隔
        QStringList pathNames;
        for (const auto& dept : departmentPath) {
            pathNames.append(dept.name());
        }
        
        return pathNames.join("\\");
        
    } catch (const std::exception& e) {
        qDebug() << "getDepartmentName failed:" << e.what();
        return "未知部门";
    } catch (...) {
        qDebug() << "getDepartmentName failed: Unknown error";
        return "未知部门";
    }
}

QString ConsumerManagementWidget::formatDate(const QDateTime& dateTime)
{
    if (dateTime.isValid()) {
        return dateTime.toString("yyyy-MM-dd hh:mm");
    }
    return "-";
}

QString ConsumerManagementWidget::formatBoolValue(bool value)
{
    return value ? "启用" : "禁用";
}

void ConsumerManagementWidget::updateStatusBar()
{
    int total = m_filteredConsumers.size();
    int allTotal = m_allConsumers.size();
    
    if (total == allTotal) {
        m_totalLabel->setText(QString("总计：%1 条记录").arg(total));
    } else {
        m_totalLabel->setText(QString("显示：%1 条记录 (共 %2 条)").arg(total).arg(allTotal));
    }
}

void ConsumerManagementWidget::updateButtonStates()
{
    bool hasSelection = !getSelectedConsumers().empty();
    
    m_editButton->setEnabled(hasSelection);
    m_deleteButton->setEnabled(hasSelection);
    m_lostButton->setEnabled(hasSelection);
}

// 功能按钮实现
void ConsumerManagementWidget::onBatchAdd()
{
    m_statusLabel->setText("批量添加功能待实现");
    QMessageBox::information(this, "提示", "批量添加功能正在开发中...");
}

void ConsumerManagementWidget::onAdd()
{
    // 创建用户添加对话框
    ConsumerDialog* userDialog = new ConsumerDialog(m_databaseProvider, this, ConsumerDialog::Mode::Add);
    
    // 设置为非模态对话框
    userDialog->setWindowModality(Qt::NonModal);
    
    // 连接对话框的完成信号
    connect(userDialog, &QDialog::accepted, this, [this]() {
        // 刷新表格
        refreshConsumerTable();
        m_statusLabel->setText("添加用户完成");
    });
    
    connect(userDialog, &QDialog::rejected, this, [this]() {
        m_statusLabel->setText("取消添加用户");
    });
    
    // 设置对话框属性
    userDialog->setAttribute(Qt::WA_DeleteOnClose);
    
    // 显示对话框
    userDialog->show();
}

void ConsumerManagementWidget::onEdit()
{
    auto selectedConsumers = getSelectedConsumers();
    if (selectedConsumers.empty()) {
        QMessageBox::warning(this, "警告", "请先选择要编辑的门禁卡持有者！");
        return;
    }
    
    if (selectedConsumers.size() > 1) {
        QMessageBox::warning(this, "警告", "一次只能编辑一个门禁卡持有者，请选择单个用户！");
        return;
    }
    
    // 获取选中的用户ID
    int userId = selectedConsumers[0].id();
    
    // 创建用户编辑对话框
    ConsumerDialog* userDialog = new ConsumerDialog(m_databaseProvider, this, ConsumerDialog::Mode::Edit, userId);
    
    // 设置为非模态对话框
    userDialog->setWindowModality(Qt::NonModal);
    
    // 连接对话框的完成信号
    connect(userDialog, &QDialog::accepted, this, [this]() {
        // 刷新表格
        refreshConsumerTable();
        m_statusLabel->setText("编辑用户完成");
    });
    
    connect(userDialog, &QDialog::rejected, this, [this]() {
        m_statusLabel->setText("取消编辑用户");
    });
    
    // 设置对话框属性
    userDialog->setAttribute(Qt::WA_DeleteOnClose);
    
    // 显示对话框
    userDialog->show();
}

void ConsumerManagementWidget::onDelete()
{
    auto selectedConsumers = getSelectedConsumers();
    if (selectedConsumers.empty()) {
        QMessageBox::warning(this, "警告", "请先选择要删除的门禁卡持有者！");
        return;
    }
    
    QString message;
    if (selectedConsumers.size() == 1) {
        message = QString("确定要删除门禁卡持有者 \"%1\" 吗？").arg(selectedConsumers[0].getDisplayName());
    } else {
        message = QString("确定要删除选中的 %1 个门禁卡持有者吗？").arg(selectedConsumers.size());
    }
    
    int ret = QMessageBox::question(this, "确认删除", message, 
                                   QMessageBox::Yes | QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        try {
            std::vector<int> consumerIds;
            for (const auto& consumer : selectedConsumers) {
                consumerIds.push_back(consumer.id());
            }
            
            if (m_consumerDao->batchDelete(consumerIds)) {
                QMessageBox::information(this, "成功", QString("成功删除 %1 个门禁卡持有者").arg(selectedConsumers.size()));
                refreshConsumerTable();
                m_statusLabel->setText("删除门禁卡持有者完成");
            } else {
                QMessageBox::critical(this, "错误", "删除门禁卡持有者失败！");
                m_statusLabel->setText("删除门禁卡持有者失败");
            }
        } catch (const std::exception& e) {
            QMessageBox::critical(this, "错误", QString("删除门禁卡持有者时发生错误：%1").arg(e.what()));
            m_statusLabel->setText("删除门禁卡持有者出错");
        }
    }
}

void ConsumerManagementWidget::onPrint()
{
    m_statusLabel->setText("打印功能待实现");
    QMessageBox::information(this, "提示", "打印功能正在开发中...");
}

void ConsumerManagementWidget::onExport()
{
    m_statusLabel->setText("导出功能待实现");
    QMessageBox::information(this, "提示", "导出Excel功能正在开发中...");
}

void ConsumerManagementWidget::onImport()
{
    m_statusLabel->setText("导入功能待实现");
    QMessageBox::information(this, "提示", "导入门禁卡持有者功能正在开发中...");
}

void ConsumerManagementWidget::onLost()
{
    auto selectedConsumers = getSelectedConsumers();
    if (selectedConsumers.empty()) {
        QMessageBox::warning(this, "警告", "请先选择要挂失的门禁卡持有者！");
        return;
    }
    
    m_statusLabel->setText("挂失功能待实现");
    QMessageBox::information(this, "提示", "挂失功能正在开发中...");
}

void ConsumerManagementWidget::onSearch()
{
    onFilter();
}

// 搜索筛选实现
void ConsumerManagementWidget::onFilter()
{
    QString name = m_nameSearchEdit->text().trimmed();
    QString cardNumber = m_cardSearchEdit->text().trimmed();
    int departmentId = m_deptSearchCombo->currentData().toInt();
    
    if (name.isEmpty() && cardNumber.isEmpty() && departmentId == -1) {
        // 没有筛选条件，显示所有门禁卡持有者
        m_filteredConsumers = m_allConsumers;
    } else {
        // 有筛选条件，调用数据库查询
        try {
            m_filteredConsumers = m_consumerDao->getConsumersWithAdvancedFilter(name, cardNumber, departmentId);
            m_statusLabel->setText("筛选完成");
        } catch (const std::exception& e) {
            qDebug() << "Filter consumers failed:" << e.what();
            m_statusLabel->setText("筛选失败");
            QMessageBox::critical(this, "错误", QString("筛选门禁卡持有者时发生错误：%1").arg(e.what()));
            return;
        }
    }
    
    populateConsumerTable(m_filteredConsumers);
}

void ConsumerManagementWidget::onClearFilter()
{
    m_nameSearchEdit->clear();
    m_cardSearchEdit->clear();
    m_deptSearchCombo->setCurrentIndex(0);
    
    m_filteredConsumers = m_allConsumers;
    populateConsumerTable(m_filteredConsumers);
    m_statusLabel->setText("已清除筛选条件");
}

void ConsumerManagementWidget::onNameSearchChanged()
{
    // 实时搜索可以在这里实现，现在先留空
}

void ConsumerManagementWidget::onCardSearchChanged()
{
    // 实时搜索可以在这里实现，现在先留空
}

void ConsumerManagementWidget::onDeptSearchChanged()
{
    // 部门变化时可以自动筛选，现在先留空
}

// 表格操作实现
void ConsumerManagementWidget::onTableDoubleClicked(int row, int column)
{
    Q_UNUSED(column)
    
    if (row >= 0 && row < m_consumerTable->rowCount()) {
        // 双击编辑用户
        onEdit();
    }
}

void ConsumerManagementWidget::onTableSelectionChanged()
{
    updateButtonStates();
}

// 辅助方法实现
std::vector<Consumer> ConsumerManagementWidget::filterConsumers(const std::vector<Consumer>& consumers)
{
    std::vector<Consumer> filtered;
    for (const auto& consumer : consumers) {
        if (matchesSearchCriteria(consumer)) {
            filtered.push_back(consumer);
        }
    }
    return filtered;
}

bool ConsumerManagementWidget::matchesSearchCriteria(const Consumer& consumer)
{
    QString name = m_nameSearchEdit->text().trimmed();
    QString cardNumber = m_cardSearchEdit->text().trimmed();
    int departmentId = m_deptSearchCombo->currentData().toInt();
    
    // 姓名匹配
    if (!name.isEmpty()) {
        if (!consumer.getDisplayName().contains(name, Qt::CaseInsensitive)) {
            return false;
        }
    }
    
    // 卡号匹配
    if (!cardNumber.isEmpty()) {
        if (!consumer.workNumber().contains(cardNumber, Qt::CaseInsensitive)) {
            return false;
        }
    }
    
    // 部门匹配
    if (departmentId != -1) {
        if (consumer.departmentId() != departmentId) {
            return false;
        }
    }
    
    return true;
}

std::vector<Consumer> ConsumerManagementWidget::getSelectedConsumers()
{
    std::vector<Consumer> selectedConsumers;
    auto selectedRanges = m_consumerTable->selectedRanges();
    
    for (const auto& range : selectedRanges) {
        for (int row = range.topRow(); row <= range.bottomRow(); ++row) {
            if (row < m_consumerTable->rowCount()) {
                QTableWidgetItem* item = m_consumerTable->item(row, 0);
                if (item) {
                    int consumerId = item->data(Qt::UserRole).toInt();
                    
                    // 从过滤后的用户列表中找到对应用户
                    for (const auto& consumer : m_filteredConsumers) {
                        if (consumer.id() == consumerId) {
                            selectedConsumers.push_back(consumer);
                            break;
                        }
                    }
                }
            }
        }
    }
    
    return selectedConsumers;
}

Consumer* ConsumerManagementWidget::getCurrentConsumer()
{
    int currentRow = m_consumerTable->currentRow();
    if (currentRow >= 0 && currentRow < m_consumerTable->rowCount()) {
        QTableWidgetItem* item = m_consumerTable->item(currentRow, 0);
        if (item) {
            int consumerId = item->data(Qt::UserRole).toInt();
            
            // 从过滤后的用户列表中找到对应用户
            for (auto& consumer : m_filteredConsumers) {
                if (consumer.id() == consumerId) {
                    return &consumer;
                }
            }
        }
    }
    return nullptr;
}

} // namespace AccessControl 