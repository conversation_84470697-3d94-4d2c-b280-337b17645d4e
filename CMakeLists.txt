cmake_minimum_required(VERSION 3.5)
project(AccessControlSystem VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 为MSVC编译器添加特定选项
if(MSVC)
    add_compile_options(/Zc:__cplusplus /permissive-)
endif()

# 启用Qt自动编译功能
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 查找Qt6组件
find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Core Widgets Sql Network Multimedia MultimediaWidgets)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Core Widgets Sql Network Multimedia MultimediaWidgets)

# 添加可执行文件
add_executable(AccessControlSystem
    WIN32 MACOSX_BUNDLE

    # 主程序文件
    main.cpp
    
    # 配置模块
    src/config/DatabaseConfig.h
    src/config/DatabaseConfig.cpp
    
    # 数据库模块
    src/database/IDatabaseProvider.h
    src/database/DatabaseFactory.h
    src/database/DatabaseFactory.cpp
    src/database/DatabaseMigration.h
    src/database/DatabaseMigration.cpp
    
    # 数据库提供者
    src/database/providers/SQLiteProvider.h
    src/database/providers/SQLiteProvider.cpp
    # PostgreSQLProvider temporarily disabled due to MOC issues
    # src/database/providers/PostgreSQLProvider.h
    # src/database/providers/PostgreSQLProvider.cpp
    
    # 数据模型
    src/models/Department.h
    src/models/Department.cpp
    src/models/Area.h
    src/models/Area.cpp
    src/models/Consumer.h
    src/models/Consumer.cpp
    src/models/Operator.h
    src/models/Operator.cpp
    src/models/OperatorCard.h
    src/models/OperatorCard.cpp
    src/models/OperatorBiometric.h
    src/models/OperatorBiometric.cpp
    
    # 数据访问对象
    src/database/dao/DepartmentDao.h
    src/database/dao/DepartmentDao.cpp
    src/database/dao/AreaDao.h
    src/database/dao/AreaDao.cpp
    src/database/dao/ConsumerDao.h
    src/database/dao/ConsumerDao.cpp
    src/database/dao/OperatorDao.h
    src/database/dao/OperatorDao.cpp
    src/database/dao/OperatorCardDao.h
    src/database/dao/OperatorBiometricDao.h
    
    # 用户界面
    src/views/LoginWindow.h
    src/views/LoginWindow.cpp
    src/views/MainWindow.h
    src/views/MainWindow.cpp
    src/views/AutoLoginDialog.h
    src/views/AutoLoginDialog.cpp
    src/views/DepartmentManagementWidget.h
    src/views/DepartmentManagementWidget.cpp
    src/views/AreaManagementWidget.h
    src/views/AreaManagementWidget.cpp
    src/views/ConsumerManagementWidget.h
    src/views/ConsumerManagementWidget.cpp
    src/views/OperatorManagementWidget.h
    src/views/OperatorManagementWidget.cpp
    src/views/OperatorManagementWidget.ui
    
    # 操作员管理相关文件
    src/views/OperatorDialog.h
    src/views/OperatorDialog.cpp
    
    # 消费者管理相关文件
    src/views/ConsumerDialog.h
    src/views/ConsumerDialog.cpp
    src/views/ConsumerCardDialog.h
    src/views/ConsumerCardDialog.cpp
    src/views/ConsumerPhotoWidget.h
    src/views/ConsumerPhotoWidget.cpp
    src/views/CardLineEdit.h
    src/views/CardLineEdit.cpp
    src/views/ConsumerFingerprintWidget.h
    src/views/ConsumerFingerprintWidget.cpp
    src/views/ConsumerCardListWidget.h
    src/views/ConsumerCardListWidget.cpp
    src/views/CameraDialog.h
    src/views/CameraDialog.cpp
)

# 添加资源文件
add_custom_target(copy_resources ALL
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_SOURCE_DIR}/resources
    ${CMAKE_BINARY_DIR}/resources
    COMMENT "Copying resources to build directory"
)

# 链接库
target_link_libraries(AccessControlSystem
    PRIVATE
        Qt${QT_VERSION_MAJOR}::Core
        Qt${QT_VERSION_MAJOR}::Widgets
        Qt${QT_VERSION_MAJOR}::Sql
        Qt${QT_VERSION_MAJOR}::Network
        Qt${QT_VERSION_MAJOR}::Multimedia
        Qt${QT_VERSION_MAJOR}::MultimediaWidgets
)

# 设置包含目录
target_include_directories(AccessControlSystem
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 编译器定义
target_compile_definitions(AccessControlSystem
    PRIVATE
)

# 添加资源文件
target_sources(AccessControlSystem
    PRIVATE
    resources.qrc
)

# 设置输出目录
set_target_properties(AccessControlSystem PROPERTIES
    WIN32_EXECUTABLE TRUE
    OUTPUT_NAME "AccessControlSystem"
)

# Windows 特定设置
if(WIN32)
    set_property(TARGET AccessControlSystem PROPERTY WIN32_EXECUTABLE TRUE)
endif()

# 安装规则
install(TARGETS AccessControlSystem
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)
