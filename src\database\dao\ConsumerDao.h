#ifndef CONSUMERDAO_H
#define CONSUMERDAO_H

#include "../../models/Consumer.h"
#include "../IDatabaseProvider.h"
#include <memory>
#include <optional>
#include <vector>

namespace AccessControl {

/**
 * @brief 门禁卡持有者数据访问对象
 * 负责门禁卡持有者的数据库操作
 */
class ConsumerDao {
public:
    explicit ConsumerDao(DatabaseProviderPtr provider);
    ~ConsumerDao() = default;

    // ========== CRUD 操作 ==========
    bool create(Consumer& consumer);
    std::optional<Consumer> getById(int id);
    std::optional<Consumer> getByWorkNumber(const QString& workNumber);
    bool update(const Consumer& consumer);
    bool remove(int id);

    // ========== 查询操作 ==========
    std::vector<Consumer> getAllConsumers();
    std::vector<Consumer> getConsumersByDepartment(int departmentId);
    std::vector<Consumer> searchConsumers(const QString& searchTerm);
    std::vector<Consumer> searchConsumersByName(const QString& name);
    std::vector<Consumer> searchConsumersByCard(const QString& cardNumber);
    std::vector<Consumer> getConsumersWithAdvancedFilter(const QString& name, const QString& cardNumber, int departmentId);

    // ========== 验证操作 ==========
    bool isWorkNumberExists(const QString& workNumber, int excludeId = -1);
    bool isPhoneNumberExists(const QString& phoneNumber, int excludeId = -1);
    bool isIdNumberExists(const QString& idNumber, int excludeId = -1);

    // ========== 统计操作 ==========
    int getTotalConsumerCount();
    int getActiveConsumerCount();
    int getConsumerCountByDepartment(int departmentId);

    // ========== 批量操作 ==========
    bool batchCreate(const std::vector<Consumer>& consumers);
    bool batchUpdate(const std::vector<Consumer>& consumers);
    bool batchDelete(const std::vector<int>& consumerIds);

private:
    DatabaseProviderPtr m_provider;
    
    // ========== 辅助方法 ==========
    Consumer mapRowToConsumer(const QSqlQuery& query);
    void bindConsumerToQuery(QSqlQuery& query, const Consumer& consumer, bool includeId = false);
};

} // namespace AccessControl

#endif // CONSUMERDAO_H 